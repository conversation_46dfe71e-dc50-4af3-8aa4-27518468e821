{% layout '' %}
<script src="/assets/js/common.js"></script>
<script>
    {{ Model.Js | raw }}
</script>
<!--Marquee Text-->
<div id="head_activities"></div>
<!--<div class="topbar-slider clearfix" style=" background: var(--theme-color); padding: 10px 0;">
    <div class="container-fluid">
        <div class="marquee-text">
            <div class="top-info-bar d-flex">
                <div class="flex-item center"><a href="#;"><span class="flash-icon">⚡</span>T130P – Limited Quantity Remaining! Order Now >></a></div>
                <div class="flex-item center"><a href="#;"><b><span class="flash-icon">🎧</span>Tour Guide System - Get Free Demo Kit >></a></div>
            </div>
        </div>
    </div>
</div>-->
<!--End Marquee Text-->
<!--Header-->
<div class="topheader">
    <div class="container-fluid">
        <div class="row">
            <div class="col-10 col-sm-8 col-md-5 col-lg-4">
                <p class="email"><a href=""><i style="font-size:20px;" class="icon an an-envelope"></i></a></p>
                <p class="phone-no mx-4"><a href=""><i style="font-size:18px;" class="an an-phone"></i></a></p>
                <p class="phone-no"><a style="color: var(--theme-color);font-weight: bold;"
                                       href="/pages/conviertase-en-distribuidor">Become a Dealer</a></p>
            </div>
            <div class="col-sm-4 col-md-5 col-lg-4 d-none d-md-block d-lg-block">
            </div>
            <div class="col-2 col-sm-4 col-md-2 col-lg-4 text-right">
            </div>
        </div>
    </div>
</div>
<header class="header d-flex align-items-center header-7 header-sticky" id="main-header">
    <div class="container-fluid">
        <div class="row">
            <!--Mobile Icons-->
            <div class="col-4 col-sm-4 col-md-4 d-block d-lg-none mobile-icons">
                <!--Mobile Toggle-->
                <button type="button" class="btn--link site-header__menu js-mobile-nav-toggle mobile-nav--open">
                    <i class="icon an an-times"></i>
                    <i class="an an-bars"></i>
                </button>
                <!--End Mobile Toggle-->
                <!--Search-->
                <div class="site-search iconset" id="mobile-search">
                    <i class="icon an an-search"></i>
                </div>
                <!--End Search-->
            </div>
            <!--Mobile Icons-->
            <!--Desktop Logo-->
            <div class="logo col-4 col-sm-4 col-md-4 col-lg-3 align-self-center">
                <a href="/">
                    <img src="{{ static_path }}/assets/images/logo/retekess-logo.png" alt="Retekess" title="Retekess"/>
                </a>
            </div>
            <!--End Desktop Logo-->
            <div class="col-1 col-sm-1 col-md-1 col-lg-6 align-self-center d-menu-col">
                <!--Desktop Menu - 通过HTMX加载-->
                <div id="desktop-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select="nav#AccessibleNav">
                    <!-- 导航菜单将通过HTMX加载 -->
                </div>
                <!--End Desktop Menu-->
            </div>
            <div class="col-4 col-sm-4 col-md-4 col-lg-3 align-self-center icons-col text-right">
                <!--Search-->
                <div class="site-search iconset" id="desktop-search">
                    <i class="icon an an-search"></i>
                </div>
                <div class="search-drawer">
                    <div class="container">
                        <div class="block block-search">
                            <div class="block block-content">
                                <div class="searchField">
                                    <div class="input-box">
                                        <input type="text" name="q" value=""
                                               placeholder="{{ "blog.global.searchBtn" | translate }}..."
                                               class="input-text">
                                        <button type="submit" title="{{ "blog.global.searchBtn" | translate }}"
                                                class="action search" disabled=""><i class="icon an an-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--End Search-->
                <!--Setting Dropdown-->
                <div class="setting-link iconset" id="user-settings">
                    <i class="icon an an-cog"></i>
                </div>
                <div id="settingsBox">
                    <div class="customer-links">
                        {% if IsLogined == "true" %}
                            <!-- 已登录 -->
                            <div class="dropdown-menu" style="display:block;position:relative;border:none;">
                                <a class="dropdown-item"
                                   href="/Account/MyProfile">{{ "user.account.indexTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyOrders">{{ "user.account.orderTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyInbox">{{ "user.account.inboxTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyAddress">{{ "user.account.addressTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyCoupon">{{ "user.account.couponTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyWishList">{{ "user.account.favoriteTitle" | translate }}</a>
                                <!--<a class="dropdown-item"
                                   href="/Account/MyReview">{{ "user.account.reviewTitle" | translate }}</a>-->
                                <a class="dropdown-item"
                                   href="/account/SignOut">{{ "user.account.logOut" | translate }}</a>
                            </div>
                        {% else %}
                            <!-- 未登录 -->
                            <p><a href="/account/signin"
                                  class="btn theme-btn">{{ "user.global.sign_in" | translate }}</a></p>
                            <p class="text-center"><a href="/account/signup"
                                                      class="register">{{ "user.register.register_title" | translate }}</a>
                            </p>
                        {% endif %}

                    </div>

                    <div hx-get="/home/<USER>" hx-trigger="load" id="language-switch-container"></div>

                </div>
                <!--End Setting Dropdown-->
                <!--Wishlist-->
                <div class="wishlist-link iconset" id="wishlist-icon">
                    <a href="/Account/MyWishList">
                        <i class="icon an an-heart-o"></i>
                        <span class="wishlist-count">0</span>
                    </a>
                </div>
                <!--End Wishlist-->
                <!--Minicart Dropdown-->
                <div class="header-cart iconset" id="shopping-cart">
                    <a href="/cart" class="site-header__cart btn-minicart">
                        <div class="icon-in">
                            <i class="icon an an-shopping-cart"></i>
                            <span class="site-cart-count">0</span>
                        </div>
                    </a>
                </div>
                <!--End Minicart Dropdown-->
            </div>
        </div>
    </div>
</header>
<!--End Header-->
<!--Mobile Menu - 通过HTMX加载-->
<div id="mobile-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select=".mobile-nav-wrapper">
    <!-- 移动端导航菜单将通过HTMX加载 -->
</div>
<!--End Mobile Menu-->
<!--MiniCart Drawer-->
<!--{% assign miniCart = '/Themes/' | append: theme | append: '/Order/MiniCartDrawer' %}
    {% include miniCart -%}-->
<!--End MiniCart Drawer-->
<input type="hidden" name="operateactivity" id="operateactivity" value="{{ Model.OperateData }}"/>
<script src="/assets/js/operateactivity.js"></script>

<!-- 可视化页面配置处理脚本 -->
<script>
    // 测试脚本是否正常执行
    console.log('Header.liquid 脚本开始执行...');

    // 处理可视化页面配置
    function initVisualPageConfig() {
        console.log('开始初始化可视化页面配置...');

        try {
            // 获取可视化页面数据 - 使用更安全的方式
            var visualPageData = null;

            try {
                // 使用不转义的 JSON 过滤器直接获取数据
                var visualPageDataJson = '{{ Model.VisualPageData | json_raw }}';
                console.log('原始JSON字符串长度:', visualPageDataJson ? visualPageDataJson.length : 0);

                // 检查JSON字符串是否有效
                if (visualPageDataJson &&
                    visualPageDataJson !== '' &&
                    visualPageDataJson !== 'null' &&
                    visualPageDataJson !== '{}' &&
                    visualPageDataJson.trim().length > 0) {

                    // 尝试解析JSON
                    visualPageData = JSON.parse(visualPageDataJson);
                    console.log('可视化页面数据解析成功');
                } else {
                    console.log('JSON字符串为空或无效，使用默认结构');
                    visualPageData = { PluginsByType: {} };
                }
            } catch (parseError) {
                console.error('可视化页面数据解析失败:', parseError);
                console.log('JSON解析错误位置:', parseError.message);

                // 截取部分JSON字符串用于调试（避免控制台输出过长）
                if (visualPageDataJson && visualPageDataJson.length > 200) {
                    console.log('JSON字符串前200字符:', visualPageDataJson.substring(0, 200));
                    console.log('JSON字符串后200字符:', visualPageDataJson.substring(visualPageDataJson.length - 200));
                } else {
                    console.log('完整JSON字符串:', visualPageDataJson);
                }

                // 设置默认结构
                visualPageData = { PluginsByType: {} };
            }

            // 调试：显示所有可用的插件类型
            if (visualPageData && visualPageData.PluginsByType) {
                var pluginTypes = Object.keys(visualPageData.PluginsByType);
                console.log('可用的插件类型:', pluginTypes);

                // 只显示header插件的详细信息
                if (visualPageData.PluginsByType.header) {
                    console.log('Header插件数量:', visualPageData.PluginsByType.header.length);
                }
            }

            // 检查是否有header插件配置
            if (visualPageData &&
                visualPageData.PluginsByType &&
                visualPageData.PluginsByType.header &&
                Array.isArray(visualPageData.PluginsByType.header)) {

                var headerPlugins = visualPageData.PluginsByType.header;
                console.log('找到header插件:', headerPlugins.length, '个');

                // 遍历header插件配置
                headerPlugins.forEach(function(plugin, index) {
                    try {
                        console.log('处理第', index + 1, '个header插件，PId:', plugin.PId);

                        // 优先使用已解析的设置对象，避免重复解析JSON
                        var settings = null;
                        if (plugin.ParsedSettings) {
                            settings = plugin.ParsedSettings;
                            console.log('使用预解析的Settings');
                        } else if (plugin.Settings) {
                            try {
                                settings = JSON.parse(plugin.Settings);
                                console.log('动态解析Settings成功');
                            } catch (settingsError) {
                                console.error('Settings JSON解析失败:', settingsError);
                                console.log('Settings内容:', plugin.Settings);
                            }
                        }

                        if (settings) {
                            console.log('应用header插件配置，PId:', plugin.PId, 'Settings:', settings);
                            applyHeaderSettings(settings);
                        } else {
                            console.log('插件没有有效的Settings配置，PId:', plugin.PId);
                        }
                    } catch (e) {
                        console.error('处理header插件失败，PId:', plugin.PId, '错误:', e);
                    }
                });
            } else {
                console.log('未找到header插件配置或配置格式不正确');
            }
        } catch (e) {
            console.error('初始化可视化页面配置失败:', e);
        }
    }

    // 应用header配置设置
    function applyHeaderSettings(settings) {
        console.log('开始应用header配置:', settings);

        // 搜索框控制
        if (settings.Search === "0") {
            // 隐藏搜索框
            var desktopSearch = document.getElementById('desktop-search');
            var mobileSearch = document.getElementById('mobile-search');
            if (desktopSearch) {
                desktopSearch.style.display = 'none';
                console.log('隐藏桌面搜索框');
            }
            if (mobileSearch) {
                mobileSearch.style.display = 'none';
                console.log('隐藏移动端搜索框');
            }
        } else if (settings.Search === "1") {
            // 显示搜索框
            var desktopSearch = document.getElementById('desktop-search');
            var mobileSearch = document.getElementById('mobile-search');
            if (desktopSearch) {
                desktopSearch.style.display = 'block';
                console.log('显示桌面搜索框');
            }
            if (mobileSearch) {
                mobileSearch.style.display = 'block';
                console.log('显示移动端搜索框');
            }

            // 设置搜索框占位符
            if (settings.SearchPlaceholder) {
                var searchInput = document.querySelector('.search-drawer input[name="q"]');
                if (searchInput) {
                    searchInput.placeholder = settings.SearchPlaceholder;
                    console.log('设置搜索框占位符:', settings.SearchPlaceholder);
                }
            }
        }

        // 用户图标控制
        if (settings.User === "0") {
            // 隐藏用户设置图标
            var userSettings = document.getElementById('user-settings');
            if (userSettings) {
                userSettings.style.display = 'none';
                console.log('隐藏用户设置图标');
            }
        } else if (settings.User === "1") {
            // 显示用户设置图标
            var userSettings = document.getElementById('user-settings');
            if (userSettings) {
                userSettings.style.display = 'block';
                console.log('显示用户设置图标');
            }
        }

        // 购物车控制
        if (settings.ShoppingCart === "0") {
            // 隐藏购物车图标
            var shoppingCart = document.getElementById('shopping-cart');
            if (shoppingCart) {
                shoppingCart.style.display = 'none';
                console.log('隐藏购物车图标');
            }
        } else if (settings.ShoppingCart === "1") {
            // 显示购物车图标
            var shoppingCart = document.getElementById('shopping-cart');
            if (shoppingCart) {
                shoppingCart.style.display = 'block';
                console.log('显示购物车图标');
            }
        }

        console.log('Header配置应用完成');
    }

    // 页面加载完成后初始化配置
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，开始初始化...');
        initVisualPageConfig();
    });

    // 如果是通过HTMX加载的页面，也需要初始化
    document.addEventListener('htmx:afterSwap', function() {
        console.log('HTMX页面交换完成，重新初始化...');
        initVisualPageConfig();
    });
</script>

<!-- 全局搜索功能脚本 -->
<script>
    // t200主题全局搜索功能实现
    function initGlobalSearchT200() {
        // 获取搜索框和搜索按钮
        const searchInput = document.querySelector('.search-drawer input[name="q"]');
        const searchButton = document.querySelector('.search-drawer .action.search');

        if (!searchInput || !searchButton) {
            console.log('t200搜索元素未找到');
            return;
        }

        // 搜索函数
        function performSearch() {
            const keyword = searchInput.value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            // 构建搜索URL - 始终跳转到/shop页面
            let url = '/collections';
            let params = [];

            // 添加keyword参数
            params.push(`keyword=${encodeURIComponent(keyword)}`);

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            // 关闭搜索抽屉
            document.querySelector('.search-drawer').classList.remove('search-drawer-open');
            document.querySelector('.mask-overlay')?.remove();

            // 跳转到搜索结果页面
            window.location.href = url;
        }

        // 移除按钮的disabled属性
        searchButton.removeAttribute('disabled');

        // 绑定搜索按钮点击事件
        searchButton.addEventListener('click', function (e) {
            e.preventDefault();
            performSearch();
        });

        // 绑定回车键事件
        searchInput.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' || e.keyCode === 13) {
                e.preventDefault();
                performSearch();
            }
        });

    }

    // 页面加载完成后初始化搜索功能
    document.addEventListener('DOMContentLoaded', function () {
        initGlobalSearchT200();
    });

    // 如果是通过HTMX加载的页面，也需要初始化
    document.addEventListener('htmx:afterSwap', function () {
        initGlobalSearchT200();
    });

    $(function () {

        //获取用户配置
        GetUserConf();

    })

    //获取用户配置
    function GetUserConf() {
        $.ajax({
            url: '/Account/GetUserConf',
            method: 'GET',
            contentType: 'application/json',
            success: function (data) {

                $(".wishlist-count").text(data.wishlistCount);
                $(".site-cart-count").text(data.cartCount);
                $(".cart-count").text(data.cartCount);

            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Please try again later.', null, null, {showIcon: true});
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    }

</script>